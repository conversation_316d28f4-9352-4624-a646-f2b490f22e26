/* ===== LOGIN PAGE STYLES ===== */

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: #ffffff;
    color: #000000;
    line-height: 1.6;
    overflow-x: hidden;
}

/* ===== CONTAINER & LAYOUT ===== */

.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    padding: 20px;
}

.bg-pattern {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(circle at 25% 25%, rgba(0, 0, 0, 0.02) 1px, transparent 1px),
        radial-gradient(circle at 75% 75%, rgba(0, 0, 0, 0.02) 1px, transparent 1px);
    background-size: 50px 50px;
    z-index: -1;
}

.login-card {
    background: #ffffff;
    border: 2px solid #000000;
    border-radius: 0;
    padding: 40px;
    width: 100%;
    max-width: 420px;
    box-shadow: 8px 8px 0px #000000;
    position: relative;
}

/* ===== HEADER SECTION ===== */

.login-header {
    text-align: center;
    margin-bottom: 40px;
}

.logo-section {
    margin-bottom: 20px;
}

.bot-avatar {
    font-size: 48px;
    margin-bottom: 16px;
    display: block;
}

.admin-avatar {
    font-size: 48px;
}

.login-title {
    font-size: 32px;
    font-weight: 700;
    color: #000000;
    margin-bottom: 8px;
    letter-spacing: -0.5px;
}

.login-subtitle {
    font-size: 16px;
    font-weight: 500;
    color: #666666;
    margin-bottom: 0;
}

.institute-name {
    font-size: 14px;
    font-weight: 400;
    color: #888888;
    border-top: 1px solid #e0e0e0;
    padding-top: 16px;
    margin-top: 16px;
}

/* ===== FORM STYLES ===== */

.login-form {
    margin-bottom: 30px;
}

.form-group {
    margin-bottom: 24px;
}

.form-label {
    display: block;
    font-size: 14px;
    font-weight: 600;
    color: #000000;
    margin-bottom: 8px;
}

.input-wrapper {
    position: relative;
    display: flex;
    align-items: center;
}

.input-wrapper input {
    width: 100%;
    padding: 16px 16px 16px 48px;
    border: 2px solid #000000;
    border-radius: 0;
    font-size: 16px;
    font-weight: 400;
    background: #ffffff;
    color: #000000;
    transition: all 0.2s ease;
}

.input-wrapper input:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.1);
    transform: translateX(-2px) translateY(-2px);
}

.input-wrapper input::placeholder {
    color: #999999;
    font-weight: 400;
}

.input-icon {
    position: absolute;
    left: 16px;
    color: #666666;
    z-index: 2;
    pointer-events: none;
}

.password-toggle {
    position: absolute;
    right: 16px;
    background: none;
    border: none;
    color: #666666;
    cursor: pointer;
    padding: 4px;
    border-radius: 4px;
    transition: color 0.2s ease;
}

.password-toggle:hover {
    color: #000000;
}

/* ===== ERROR MESSAGE ===== */

.error-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background: #ffffff;
    border: 2px solid #000000;
    color: #000000;
    font-size: 14px;
    font-weight: 500;
    margin-bottom: 20px;
    border-radius: 0;
}

.error-message svg {
    flex-shrink: 0;
}

/* ===== BUTTONS ===== */

.login-btn {
    width: 100%;
    padding: 16px 24px;
    background: #000000;
    color: #ffffff;
    border: 2px solid #000000;
    border-radius: 0;
    font-size: 16px;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.2s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 8px;
    position: relative;
}

.login-btn:hover {
    background: #ffffff;
    color: #000000;
    box-shadow: 4px 4px 0px rgba(0, 0, 0, 0.1);
    transform: translateX(-2px) translateY(-2px);
}

.login-btn:active {
    transform: translateX(0) translateY(0);
    box-shadow: 2px 2px 0px rgba(0, 0, 0, 0.1);
}

.login-btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    transform: none;
    box-shadow: none;
}

.btn-loader {
    display: flex;
    align-items: center;
    justify-content: center;
}

.spinner {
    width: 20px;
    height: 20px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* ===== LINKS ===== */

.forgot-password {
    text-align: center;
    margin-top: 20px;
}

.forgot-password a {
    color: #666666;
    text-decoration: none;
    font-size: 14px;
    font-weight: 500;
    transition: color 0.2s ease;
}

.forgot-password a:hover {
    color: #000000;
    text-decoration: underline;
}

/* ===== FOOTER ===== */

.login-footer {
    text-align: center;
    padding-top: 24px;
    border-top: 1px solid #e0e0e0;
}

.login-footer p {
    font-size: 14px;
    color: #888888;
    margin-bottom: 12px;
}

.admin-link a,
.student-link a {
    color: #000000;
    text-decoration: none;
    font-size: 14px;
    font-weight: 600;
    padding: 8px 16px;
    border: 1px solid #000000;
    border-radius: 0;
    transition: all 0.2s ease;
    display: inline-block;
}

.admin-link a:hover,
.student-link a:hover {
    background: #000000;
    color: #ffffff;
}

.copyright {
    position: absolute;
    bottom: 20px;
    left: 50%;
    transform: translateX(-50%);
    text-align: center;
}

.copyright p {
    font-size: 12px;
    color: #999999;
    font-weight: 400;
}

/* ===== ADMIN THEME ===== */

.admin-theme .login-card {
    border-color: #000000;
    box-shadow: 8px 8px 0px #000000;
}

.admin-theme .admin-btn {
    background: #000000;
    border-color: #000000;
}

.admin-theme .admin-btn:hover {
    background: #ffffff;
    color: #000000;
}

/* ===== RESPONSIVE DESIGN ===== */

@media (max-width: 480px) {
    .login-container {
        padding: 16px;
    }
    
    .login-card {
        padding: 24px;
        box-shadow: 4px 4px 0px #000000;
    }
    
    .login-title {
        font-size: 28px;
    }
    
    .bot-avatar,
    .admin-avatar {
        font-size: 40px;
    }
    
    .input-wrapper input {
        padding: 14px 14px 14px 44px;
        font-size: 16px;
    }
    
    .input-icon {
        left: 14px;
    }
    
    .password-toggle {
        right: 14px;
    }
    
    .login-btn {
        padding: 14px 20px;
        font-size: 15px;
    }
}

@media (max-width: 360px) {
    .login-card {
        padding: 20px;
    }
    
    .login-title {
        font-size: 24px;
    }
    
    .bot-avatar,
    .admin-avatar {
        font-size: 36px;
    }
}
