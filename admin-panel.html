<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>BitBot Admin Panel - Bhagwant Institute of Technology</title>

    <!-- Favicon -->
    <link rel="icon" href="assets/favicon.svg" type="image/svg+xml">
    <link rel="alternate icon" href="assets/favicon.ico" type="image/x-icon">
    <link rel="shortcut icon" href="assets/favicon.ico">

    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="css/dash-style.css">

    <!-- Firebase will be initialized by firebase-service.js -->
</head>
<body>
    <!-- Admin Panel Container -->
    <div class="admin-panel">
        <!-- Top Navigation Bar -->
        <header class="admin-header">
            <div class="header-left">
                <div class="logo-section">
                    <div class="bot-avatar">🤖</div>
                    <h1 class="admin-title">BitBot Admin Panel</h1>
                </div>
            </div>
            <div class="header-right">
                <button class="theme-toggle" id="themeToggle">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <circle cx="12" cy="12" r="5"></circle>
                        <line x1="12" y1="1" x2="12" y2="3"></line>
                        <line x1="12" y1="21" x2="12" y2="23"></line>
                        <line x1="4.22" y1="4.22" x2="5.64" y2="5.64"></line>
                        <line x1="18.36" y1="18.36" x2="19.78" y2="19.78"></line>
                        <line x1="1" y1="12" x2="3" y2="12"></line>
                        <line x1="21" y1="12" x2="23" y2="12"></line>
                        <line x1="4.22" y1="19.78" x2="5.64" y2="18.36"></line>
                        <line x1="18.36" y1="5.64" x2="19.78" y2="4.22"></line>
                    </svg>
                </button>
                <button class="logout-btn" id="logoutBtn">
                    <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"></path>
                        <polyline points="16,17 21,12 16,7"></polyline>
                        <line x1="21" y1="12" x2="9" y2="12"></line>
                    </svg>
                    Logout
                </button>
            </div>
        </header>

        <!-- Main Content Area -->
        <div class="admin-content">
            <!-- Sidebar -->
            <aside class="sidebar" id="sidebar">
                <nav class="sidebar-nav">
                    <button class="nav-item active" data-section="dashboard">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <rect x="3" y="3" width="7" height="7"></rect>
                            <rect x="14" y="3" width="7" height="7"></rect>
                            <rect x="14" y="14" width="7" height="7"></rect>
                            <rect x="3" y="14" width="7" height="7"></rect>
                        </svg>
                        Dashboard
                    </button>
                    <button class="nav-item" data-section="add-data">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <line x1="12" y1="5" x2="12" y2="19"></line>
                            <line x1="5" y1="12" x2="19" y2="12"></line>
                        </svg>
                        Add Data
                    </button>
                    <button class="nav-item" data-section="manage-data">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"></path>
                            <polyline points="14,2 14,8 20,8"></polyline>
                            <line x1="16" y1="13" x2="8" y2="13"></line>
                            <line x1="16" y1="17" x2="8" y2="17"></line>
                            <polyline points="10,9 9,9 8,9"></polyline>
                        </svg>
                        Manage Data
                    </button>
                    <button class="nav-item" data-section="settings">
                        <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="3"></circle>
                            <path d="M19.4 15a1.65 1.65 0 0 0 .33 1.82l.06.06a2 2 0 0 1 0 2.83 2 2 0 0 1-2.83 0l-.06-.06a1.65 1.65 0 0 0-1.82-.33 1.65 1.65 0 0 0-1 1.51V21a2 2 0 0 1-2 2 2 2 0 0 1-2-2v-.09A1.65 1.65 0 0 0 9 19.4a1.65 1.65 0 0 0-1.82.33l-.06.06a2 2 0 0 1-2.83 0 2 2 0 0 1 0-2.83l.06-.06a1.65 1.65 0 0 0 .33-1.82 1.65 1.65 0 0 0-1.51-1H3a2 2 0 0 1-2-2 2 2 0 0 1 2-2h.09A1.65 1.65 0 0 0 4.6 9a1.65 1.65 0 0 0-.33-1.82l-.06-.06a2 2 0 0 1 0-2.83 2 2 0 0 1 2.83 0l.06.06a1.65 1.65 0 0 0 1.82.33H9a1.65 1.65 0 0 0 1 1.51V3a2 2 0 0 1 2-2 2 2 0 0 1 2 2v.09a1.65 1.65 0 0 0 1 1.51 1.65 1.65 0 0 0 1.82-.33l.06-.06a2 2 0 0 1 2.83 0 2 2 0 0 1 0 2.83l-.06.06a1.65 1.65 0 0 0-.33 1.82V9a1.65 1.65 0 0 0 1.51 1H21a2 2 0 0 1 2 2 2 2 0 0 1-2 2h-.09a1.65 1.65 0 0 0-1.51 1z"></path>
                        </svg>
                        Settings
                    </button>
                </nav>
            </aside>

            <!-- Mobile Menu Toggle -->
            <button class="mobile-menu-toggle" id="mobileMenuToggle">
                <svg width="24" height="24" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                    <line x1="3" y1="6" x2="21" y2="6"></line>
                    <line x1="3" y1="12" x2="21" y2="12"></line>
                    <line x1="3" y1="18" x2="21" y2="18"></line>
                </svg>
            </button>

            <!-- Main Panel -->
            <main class="main-panel">
                <!-- Dashboard Section -->
                <section class="content-section active" id="dashboard-section">
                    <div class="section-header">
                        <h2>Dashboard</h2>
                        <p>Welcome to BitBot Admin Panel</p>
                    </div>
                    
                    <div class="stats-grid">
                        <div class="stat-card">
                            <div class="stat-icon">📚</div>
                            <div class="stat-info">
                                <h3 id="totalRecords">0</h3>
                                <p>Total Records</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">📅</div>
                            <div class="stat-info">
                                <h3 id="timetableCount">0</h3>
                                <p>Timetable Entries</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">💰</div>
                            <div class="stat-info">
                                <h3 id="feeCount">0</h3>
                                <p>Fee Records</p>
                            </div>
                        </div>
                        <div class="stat-card">
                            <div class="stat-icon">👨‍🏫</div>
                            <div class="stat-info">
                                <h3 id="facultyCount">0</h3>
                                <p>Faculty Info</p>
                            </div>
                        </div>
                    </div>
                </section>

                <!-- Add Data Section -->
                <section class="content-section" id="add-data-section">
                    <div class="section-header">
                        <h2>Add New Record</h2>
                        <p>Add new information to the BitBot database</p>
                    </div>
                    
                    <div class="form-container">
                        <form class="add-form" id="addForm">
                            <div class="form-group">
                                <label for="category">Category</label>
                                <select id="category" name="category" required>
                                    <option value="">Select Category</option>
                                    <option value="timetable">📅 Timetable</option>
                                    <option value="fee">💰 Fee Information</option>
                                    <option value="faculty">👨‍🏫 Faculty Information</option>
                                    <option value="notice">📢 Notice/Announcement</option>
                                </select>
                            </div>
                            
                            <div class="form-group">
                                <label for="title">Title</label>
                                <input type="text" id="title" name="title" placeholder="Enter title..." required>
                            </div>
                            
                            <div class="form-group">
                                <label for="description">Description</label>
                                <textarea id="description" name="description" placeholder="Enter detailed description..." rows="4" required></textarea>
                            </div>
                            
                            <button type="submit" class="submit-btn">
                                <svg width="18" height="18" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <line x1="12" y1="5" x2="12" y2="19"></line>
                                    <line x1="5" y1="12" x2="19" y2="12"></line>
                                </svg>
                                Add Record
                            </button>
                        </form>
                    </div>
                </section>

                <!-- Manage Data Section -->
                <section class="content-section" id="manage-data-section">
                    <div class="section-header">
                        <h2>Manage Data</h2>
                        <p>View, edit, and delete existing records</p>
                    </div>
                    
                    <div class="table-container">
                        <div class="table-header">
                            <div class="search-filter">
                                <input type="text" id="searchInput" placeholder="Search records..." class="search-input">
                                <select id="filterCategory" class="filter-select">
                                    <option value="">All Categories</option>
                                    <option value="timetable">Timetable</option>
                                    <option value="fee">Fee Information</option>
                                    <option value="faculty">Faculty Information</option>
                                    <option value="notice">Notice/Announcement</option>
                                </select>
                            </div>
                        </div>
                        
                        <div class="data-table-wrapper">
                            <table class="data-table" id="dataTable">
                                <thead>
                                    <tr>
                                        <th>ID</th>
                                        <th>Category</th>
                                        <th>Title</th>
                                        <th>Description</th>
                                        <th>Date Added</th>
                                        <th>Actions</th>
                                    </tr>
                                </thead>
                                <tbody id="tableBody">
                                    <!-- Dynamic content will be inserted here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </section>

                <!-- Settings Section -->
                <section class="content-section" id="settings-section">
                    <div class="section-header">
                        <h2>Settings</h2>
                        <p>Configure admin panel preferences</p>
                    </div>
                    
                    <div class="settings-container">
                        <div class="setting-item">
                            <div class="setting-info">
                                <h3>Theme Preference</h3>
                                <p>Choose between light and dark mode</p>
                            </div>
                            <button class="setting-toggle" id="themeSettingToggle" role="switch" aria-pressed="false" aria-label="Theme is light mode">
                                <span class="toggle-slider"></span>
                            </button>
                        </div>
                        
                        <div class="setting-item">
                            <div class="setting-info">
                                <h3>Auto-save</h3>
                                <p>Automatically save changes</p>
                            </div>
                            <button class="setting-toggle active" id="autosaveToggle" role="switch" aria-pressed="true" aria-label="Auto-save is enabled">
                                <span class="toggle-slider"></span>
                            </button>
                        </div>

                        <!-- Password Change Section -->
                        <div class="setting-item password-change-section">
                            <div class="setting-info">
                                <h3>Change Admin Password</h3>
                                <p>Update your admin login password</p>
                            </div>
                            <button class="setting-btn" id="changePasswordBtn">
                                <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                                    <rect x="3" y="11" width="18" height="11" rx="2" ry="2"></rect>
                                    <circle cx="12" cy="16" r="1"></circle>
                                    <path d="M7 11V7a5 5 0 0 1 10 0v4"></path>
                                </svg>
                                Change Password
                            </button>
                        </div>
                    </div>
                </section>
            </main>
        </div>
    </div>

    <!-- Edit Modal -->
    <div class="modal-overlay" id="editModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Edit Record</h3>
                <button class="modal-close" id="closeModal">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form class="edit-form" id="editForm">
                    <input type="hidden" id="editId">
                    <div class="form-group">
                        <label for="editCategory">Category</label>
                        <select id="editCategory" name="category" required>
                            <option value="timetable">📅 Timetable</option>
                            <option value="fee">💰 Fee Information</option>
                            <option value="faculty">👨‍🏫 Faculty Information</option>
                            <option value="notice">📢 Notice/Announcement</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label for="editTitle">Title</label>
                        <input type="text" id="editTitle" name="title" required>
                    </div>
                    <div class="form-group">
                        <label for="editDescription">Description</label>
                        <textarea id="editDescription" name="description" rows="4" required></textarea>
                    </div>
                    <div class="modal-actions">
                        <button type="button" class="cancel-btn" id="cancelEdit">Cancel</button>
                        <button type="submit" class="save-btn">Save Changes</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <!-- Password Change Modal -->
    <div class="modal-overlay" id="passwordModal">
        <div class="modal">
            <div class="modal-header">
                <h3>Change Admin Password</h3>
                <button class="modal-close" id="closePasswordModal">
                    <svg width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                        <line x1="18" y1="6" x2="6" y2="18"></line>
                        <line x1="6" y1="6" x2="18" y2="18"></line>
                    </svg>
                </button>
            </div>
            <div class="modal-body">
                <form class="password-form" id="passwordForm">
                    <div class="form-group">
                        <label for="currentPassword">Current Password</label>
                        <input type="password" id="currentPassword" name="currentPassword" placeholder="Enter current password" required>
                    </div>
                    <div class="form-group">
                        <label for="newPassword">New Password</label>
                        <input type="password" id="newPassword" name="newPassword" placeholder="Enter new password" required>
                    </div>
                    <div class="form-group">
                        <label for="confirmPassword">Confirm New Password</label>
                        <input type="password" id="confirmPassword" name="confirmPassword" placeholder="Confirm new password" required>
                    </div>

                    <!-- Error Message -->
                    <div class="error-message" id="passwordErrorMessage" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <circle cx="12" cy="12" r="10"></circle>
                            <line x1="15" y1="9" x2="9" y2="15"></line>
                            <line x1="9" y1="9" x2="15" y2="15"></line>
                        </svg>
                        <span id="passwordErrorText">Error message</span>
                    </div>

                    <!-- Success Message -->
                    <div class="success-message" id="passwordSuccessMessage" style="display: none;">
                        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
                            <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                            <polyline points="22,4 12,14.01 9,11.01"></polyline>
                        </svg>
                        <span id="passwordSuccessText">Password changed successfully!</span>
                    </div>

                    <div class="modal-actions">
                        <button type="button" class="cancel-btn" id="cancelPasswordChange">Cancel</button>
                        <button type="submit" class="save-btn">Change Password</button>
                    </div>
                </form>
            </div>
        </div>
    </div>

    <script src="firebase-config.js"></script>
    <script src="firebase-service.js"></script>
    <script src="auth.js"></script>
    <script src="js/dash-script.js"></script>
    <script>
        // Wait for DOM and Firebase service to be ready
        document.addEventListener('DOMContentLoaded', async function() {
            console.log('Admin panel DOM loaded, checking authentication...');

            // Wait a bit for Firebase service to be fully initialized
            await new Promise(resolve => setTimeout(resolve, 500));

            // Protect this page and initialize logout
            if (typeof protectPage === 'function') {
                console.log('Calling protectPage...');
                await protectPage();
                console.log('protectPage completed');
            } else {
                console.error('protectPage function not found');
            }
        });
    </script>
</body>
</html>
