/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

html, body {
    height: 100%;
    font-family: 'Poppins', sans-serif;
    background-color: #ffffff;
    color: #000000;
    line-height: 1.6;
    transition: background-color 0.3s ease, color 0.3s ease;
}

/* Dark Mode Styles */
body.dark-mode {
    background: linear-gradient(135deg, #0a0a0a 0%, #1a1a1a 100%);
    color: #e0e0e0;
}

body.dark-mode .admin-panel {
    background-color: #0f0f0f;
}

body.dark-mode .admin-header {
    background-color: #1c1c1c;
    border-bottom: 1px solid #404040;
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.7);
}

body.dark-mode .admin-title {
    color: #ffffff;
}

body.dark-mode .theme-toggle,
body.dark-mode .logout-btn {
    border: 1px solid #404040;
    color: #e0e0e0;
    background-color: #2a2a2a;
}

body.dark-mode .theme-toggle:hover,
body.dark-mode .logout-btn:hover {
    background-color: #3a3a3a;
    border-color: #555555;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.1);
}

body.dark-mode .sidebar {
    background-color: #1c1c1c;
    border-right: 1px solid #404040;
}

body.dark-mode .nav-item {
    color: #b0b0b0;
}

body.dark-mode .nav-item:hover {
    background-color: #2a2a2a;
    color: #ffffff;
}

body.dark-mode .nav-item.active {
    background-color: #ffffff;
    color: #000000;
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.2);
}

body.dark-mode .nav-item.active:hover {
    background-color: #f0f0f0;
    color: #000000;
}

body.dark-mode .main-panel {
    background: linear-gradient(135deg, #0f0f0f 0%, #1a1a1a 50%, #0f0f0f 100%);
    background-size: 400% 400%;
    animation: subtleGradient 20s ease infinite;
}

@keyframes subtleGradient {
    0% { background-position: 0% 50%; }
    50% { background-position: 100% 50%; }
    100% { background-position: 0% 50%; }
}

body.dark-mode .section-header h2 {
    color: #ffffff;
}

body.dark-mode .section-header p {
    color: #b0b0b0;
}

body.dark-mode .stat-card,
body.dark-mode .form-container,
body.dark-mode .table-container,
body.dark-mode .settings-container {
    background-color: #1c1c1c;
    border: 1px solid #404040;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

body.dark-mode .stat-card:hover {
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.4);
    border-color: #555555;
}

body.dark-mode .stat-icon {
    background-color: #2a2a2a;
}

body.dark-mode .stat-info h3 {
    color: #ffffff;
}

body.dark-mode .stat-info p {
    color: #b0b0b0;
}

body.dark-mode .form-group label {
    color: #e0e0e0;
}

body.dark-mode .form-group input,
body.dark-mode .form-group select,
body.dark-mode .form-group textarea {
    background-color: #2a2a2a;
    border: 1px solid #404040;
    color: #e0e0e0;
}

body.dark-mode .form-group input:focus,
body.dark-mode .form-group select:focus,
body.dark-mode .form-group textarea:focus {
    border-color: #ffffff;
    box-shadow: 0 0 0 3px rgba(255, 255, 255, 0.1);
}

body.dark-mode .submit-btn {
    background-color: #ffffff;
    color: #000000;
}

body.dark-mode .submit-btn:hover {
    background-color: #f0f0f0;
    box-shadow: 0 8px 25px rgba(255, 255, 255, 0.2);
}

body.dark-mode .table-header {
    background-color: #2a2a2a;
    border-bottom: 1px solid #404040;
}

body.dark-mode .search-input,
body.dark-mode .filter-select {
    background-color: #2a2a2a;
    border: 1px solid #404040;
    color: #e0e0e0;
}

body.dark-mode .search-input:focus,
body.dark-mode .filter-select:focus {
    border-color: #ffffff;
    box-shadow: 0 0 0 2px rgba(255, 255, 255, 0.1);
}

body.dark-mode .data-table {
    background-color: #1c1c1c;
}

body.dark-mode .data-table th {
    background-color: #2a2a2a;
    border-bottom: 1px solid #404040;
    color: #ffffff;
}

body.dark-mode .data-table td {
    border-bottom: 1px solid #404040;
    color: #e0e0e0;
}

body.dark-mode .data-table tbody tr:hover {
    background-color: #2a2a2a;
}

body.dark-mode .edit-btn {
    background-color: #2a2a2a;
    color: #e0e0e0;
}

body.dark-mode .edit-btn:hover {
    background-color: #3a3a3a;
}

body.dark-mode .delete-btn {
    background-color: #4a2a2a;
    color: #ff6b6b;
}

body.dark-mode .delete-btn:hover {
    background-color: #5a3a3a;
}

body.dark-mode .setting-info h3 {
    color: #ffffff;
}

body.dark-mode .setting-info p {
    color: #b0b0b0;
}

body.dark-mode .setting-toggle {
    background: linear-gradient(135deg, #374151, #4b5563);
    border-color: #4b5563;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2);
}

body.dark-mode .setting-toggle::before {
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.05) 50%, transparent 70%);
}

body.dark-mode .setting-toggle:hover {
    background: linear-gradient(135deg, #4b5563, #6b7280);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 4px 12px rgba(0, 0, 0, 0.3);
}

body.dark-mode .setting-toggle:focus {
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.3),
        0 2px 8px rgba(0, 0, 0, 0.2),
        0 0 0 3px rgba(16, 185, 129, 0.3);
}

body.dark-mode .setting-toggle.active {
    background: linear-gradient(135deg, #10b981, #059669);
    border-color: #10b981;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(16, 185, 129, 0.4);
}

body.dark-mode .setting-toggle.active:hover {
    background: linear-gradient(135deg, #059669, #047857);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 4px 16px rgba(16, 185, 129, 0.5);
}

body.dark-mode .setting-toggle.active:focus {
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(16, 185, 129, 0.4),
        0 0 0 3px rgba(16, 185, 129, 0.4);
}

body.dark-mode .toggle-slider {
    background: linear-gradient(135deg, #f9fafb, #e5e7eb);
    border-color: rgba(255, 255, 255, 0.6);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.4),
        0 1px 3px rgba(0, 0, 0, 0.3);
}

body.dark-mode .toggle-slider::after {
    background: linear-gradient(135deg, #6b7280, #4b5563);
}

body.dark-mode .setting-toggle.active .toggle-slider {
    background: linear-gradient(135deg, #ffffff, #f3f4f6);
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.5),
        0 1px 4px rgba(0, 0, 0, 0.4);
}

body.dark-mode .setting-toggle.active .toggle-slider::after {
    background: linear-gradient(135deg, #10b981, #059669);
}

/* Dark mode accessibility */
body.dark-mode .setting-toggle:focus-visible {
    outline: 2px solid #10b981;
    outline-offset: 2px;
}

body.dark-mode .modal {
    background-color: #1c1c1c;
    border: 1px solid #404040;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.8);
}

body.dark-mode .modal-overlay {
    background-color: rgba(0, 0, 0, 0.9);
}

body.dark-mode .modal-header {
    border-bottom: 1px solid #404040;
}

body.dark-mode .modal-header h3 {
    color: #ffffff;
}

body.dark-mode .modal-close {
    color: #b0b0b0;
}

body.dark-mode .modal-close:hover {
    background-color: #2a2a2a;
    color: #ffffff;
}

body.dark-mode .cancel-btn {
    background-color: #2a2a2a;
    color: #b0b0b0;
    border: 1px solid #404040;
}

body.dark-mode .cancel-btn:hover {
    background-color: #3a3a3a;
    color: #e0e0e0;
}

body.dark-mode .save-btn {
    background-color: #ffffff;
    color: #000000;
}

body.dark-mode .save-btn:hover {
    background-color: #f0f0f0;
    box-shadow: 0 4px 12px rgba(255, 255, 255, 0.2);
}

body.dark-mode .mobile-menu-toggle {
    background-color: #1c1c1c;
    border: 1px solid #404040;
    color: #e0e0e0;
}

body.dark-mode .mobile-menu-toggle:hover {
    background-color: #2a2a2a;
}

body.dark-mode .bot-avatar {
    background: linear-gradient(135deg, #ffffff, #e0e0e0);
    box-shadow: 0 4px 15px rgba(255, 255, 255, 0.1);
}

/* Admin Panel Layout */
.admin-panel {
    display: flex;
    flex-direction: column;
    height: 100vh;
    background-color: #ffffff;
    transition: background-color 0.3s ease;
}

/* Header Styles */
.admin-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 2rem;
    background-color: #ffffff;
    border-bottom: 1px solid #e0e0e0;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    z-index: 1000;
    transition: all 0.3s ease;
}

.header-left {
    display: flex;
    align-items: center;
}

.logo-section {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.bot-avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    background: linear-gradient(135deg, #000000, #333333);
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.admin-title {
    font-size: 1.5rem;
    font-weight: 600;
    color: #000000;
    transition: color 0.3s ease;
}

.header-right {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.theme-toggle,
.logout-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    padding: 0.5rem 1rem;
    border: 1px solid #e0e0e0;
    background-color: transparent;
    color: #000000;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 0.9rem;
}

.theme-toggle:hover,
.logout-btn:hover {
    background-color: #f5f5f5;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

/* Content Layout */
.admin-content {
    display: flex;
    flex: 1;
    overflow: hidden;
}

/* Sidebar Styles */
.sidebar {
    width: 250px;
    background-color: #ffffff;
    border-right: 1px solid #e0e0e0;
    padding: 2rem 0;
    transition: all 0.3s ease;
    overflow-y: auto;
}

.sidebar-nav {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
    padding: 0 1rem;
}

.nav-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border: none;
    background-color: transparent;
    color: #666666;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
    font-family: inherit;
    font-size: 0.95rem;
    text-align: left;
    width: 100%;
}

.nav-item:hover {
    background-color: #f5f5f5;
    color: #000000;
    transform: translateX(5px);
}

.nav-item.active {
    background-color: #000000;
    color: #ffffff;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.2);
}

.nav-item.active:hover {
    background-color: #333333;
    transform: translateX(5px);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
    position: fixed;
    top: 1rem;
    left: 1rem;
    z-index: 1001;
    padding: 0.5rem;
    border: 1px solid #e0e0e0;
    background-color: #ffffff;
    border-radius: 8px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.mobile-menu-toggle:hover {
    background-color: #f5f5f5;
}

/* Main Panel */
.main-panel {
    flex: 1;
    padding: 2rem;
    background-color: #fafafa;
    overflow-y: auto;
    transition: background-color 0.3s ease;
}

/* Content Sections */
.content-section {
    display: none;
    animation: fadeIn 0.5s ease;
}

.content-section.active {
    display: block;
}

@keyframes fadeIn {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

.section-header {
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 2rem;
    font-weight: 600;
    color: #000000;
    margin-bottom: 0.5rem;
    transition: color 0.3s ease;
}

.section-header p {
    color: #666666;
    font-size: 1rem;
    transition: color 0.3s ease;
}

/* Stats Grid */
.stats-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1.5rem;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
}

.stat-icon {
    font-size: 2rem;
    width: 60px;
    height: 60px;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #f5f5f5;
    border-radius: 12px;
}

.stat-info h3 {
    font-size: 2rem;
    font-weight: 700;
    color: #000000;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
}

.stat-info p {
    color: #666666;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

/* Form Styles */
.form-container {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.add-form {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-group label {
    font-weight: 500;
    color: #000000;
    font-size: 0.95rem;
    transition: color 0.3s ease;
}

.form-group input,
.form-group select,
.form-group textarea {
    padding: 0.75rem;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    transition: all 0.3s ease;
    background-color: #ffffff;
    color: #000000;
}

.form-group input:focus,
.form-group select:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 3px rgba(0, 0, 0, 0.1);
}

.submit-btn {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    padding: 1rem 2rem;
    background-color: #000000;
    color: #ffffff;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-size: 1rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
    align-self: flex-start;
}

.submit-btn:hover {
    background-color: #333333;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.2);
}

/* Table Styles */
.table-container {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    overflow: hidden;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.table-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
    background-color: #fafafa;
}

.search-filter {
    display: flex;
    gap: 1rem;
    align-items: center;
}

.search-input,
.filter-select {
    padding: 0.5rem;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    font-family: inherit;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.search-input {
    flex: 1;
    max-width: 300px;
}

.search-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #000000;
    box-shadow: 0 0 0 2px rgba(0, 0, 0, 0.1);
}

.data-table-wrapper {
    overflow-x: auto;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
    background-color: #ffffff;
    transition: background-color 0.3s ease;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: left;
    border-bottom: 1px solid #e0e0e0;
    transition: all 0.3s ease;
}

.data-table th {
    background-color: #f5f5f5;
    font-weight: 600;
    color: #000000;
    font-size: 0.9rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.data-table td {
    color: #333333;
    font-size: 0.9rem;
}

.data-table tbody tr:hover {
    background-color: #f9f9f9;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 0.5rem;
}

.edit-btn,
.delete-btn {
    padding: 0.5rem;
    border: none;
    border-radius: 6px;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
}

.edit-btn {
    background-color: #f0f0f0;
    color: #333333;
}

.edit-btn:hover {
    background-color: #e0e0e0;
    transform: scale(1.1);
}

.delete-btn {
    background-color: #ffe6e6;
    color: #cc0000;
}

.delete-btn:hover {
    background-color: #ffcccc;
    transform: scale(1.1);
}

/* Settings Styles */
.settings-container {
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 12px;
    padding: 2rem;
    box-shadow: 0 4px 15px rgba(0, 0, 0, 0.05);
    transition: all 0.3s ease;
}

.setting-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem 0;
    border-bottom: 1px solid #e0e0e0;
}

.setting-item:last-child {
    border-bottom: none;
}

.setting-info h3 {
    font-size: 1.1rem;
    font-weight: 500;
    color: #000000;
    margin-bottom: 0.25rem;
    transition: color 0.3s ease;
}

.setting-info p {
    color: #666666;
    font-size: 0.9rem;
    transition: color 0.3s ease;
}

.setting-toggle {
    position: relative;
    width: 56px;
    height: 30px;
    background: linear-gradient(135deg, #e5e7eb, #d1d5db);
    border: 2px solid #e5e7eb;
    border-radius: 15px;
    cursor: pointer;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    outline: none;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05);
    overflow: hidden;
}

.setting-toggle::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(45deg, transparent 30%, rgba(255, 255, 255, 0.1) 50%, transparent 70%);
    transform: translateX(-100%);
    transition: transform 0.6s ease;
}

.setting-toggle:hover {
    transform: scale(1.02);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 4px 12px rgba(0, 0, 0, 0.1);
}

.setting-toggle:hover::before {
    transform: translateX(100%);
}

.setting-toggle:focus {
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.1),
        0 2px 8px rgba(0, 0, 0, 0.05),
        0 0 0 3px rgba(79, 70, 229, 0.2);
}

.setting-toggle.active {
    background: linear-gradient(135deg, #4f46e5, #6366f1);
    border-color: #4f46e5;
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(79, 70, 229, 0.3);
}

.setting-toggle.active:hover {
    background: linear-gradient(135deg, #4338ca, #4f46e5);
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 4px 16px rgba(79, 70, 229, 0.4);
}

.setting-toggle.active:focus {
    box-shadow:
        inset 0 2px 4px rgba(0, 0, 0, 0.2),
        0 2px 8px rgba(79, 70, 229, 0.3),
        0 0 0 3px rgba(79, 70, 229, 0.3);
}

.toggle-slider {
    position: absolute;
    top: 2px;
    left: 2px;
    width: 24px;
    height: 24px;
    background: linear-gradient(135deg, #ffffff, #f8fafc);
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    box-shadow:
        0 2px 8px rgba(0, 0, 0, 0.15),
        0 1px 3px rgba(0, 0, 0, 0.1);
    border: 1px solid rgba(255, 255, 255, 0.8);
    display: flex;
    align-items: center;
    justify-content: center;
}

.toggle-slider::after {
    content: '';
    width: 8px;
    height: 8px;
    background: linear-gradient(135deg, #d1d5db, #9ca3af);
    border-radius: 50%;
    transition: all 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    opacity: 0.6;
}

.setting-toggle.active .toggle-slider {
    transform: translateX(26px);
    background: linear-gradient(135deg, #ffffff, #f1f5f9);
    box-shadow:
        0 3px 12px rgba(0, 0, 0, 0.2),
        0 1px 4px rgba(0, 0, 0, 0.15);
}

.setting-toggle.active .toggle-slider::after {
    background: linear-gradient(135deg, #4f46e5, #6366f1);
    opacity: 1;
    transform: scale(1.2);
}

/* Animation for toggle state change */
@keyframes toggleOn {
    0% {
        transform: translateX(2px) scale(0.9);
    }
    50% {
        transform: translateX(14px) scale(1.1);
    }
    100% {
        transform: translateX(26px) scale(1);
    }
}

@keyframes toggleOff {
    0% {
        transform: translateX(26px) scale(0.9);
    }
    50% {
        transform: translateX(14px) scale(1.1);
    }
    100% {
        transform: translateX(2px) scale(1);
    }
}

.setting-toggle.active .toggle-slider {
    animation: toggleOn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

.setting-toggle:not(.active) .toggle-slider {
    animation: toggleOff 0.4s cubic-bezier(0.4, 0, 0.2, 1);
}

/* Accessibility improvements */
.setting-toggle[aria-pressed="true"] {
    /* Active state already handled above */
}

.setting-toggle:focus-visible {
    outline: 2px solid #4f46e5;
    outline-offset: 2px;
}

/* Reduced motion support */
@media (prefers-reduced-motion: reduce) {
    .setting-toggle,
    .toggle-slider,
    .toggle-slider::after {
        transition: none;
        animation: none;
    }

    .setting-toggle::before {
        display: none;
    }
}

/* Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 2000;
    transition: all 0.3s ease;
}

.modal-overlay.active {
    display: flex;
}

.modal {
    background-color: #ffffff;
    border-radius: 12px;
    width: 90%;
    max-width: 500px;
    max-height: 90vh;
    overflow-y: auto;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.3);
    animation: modalSlideIn 0.3s ease;
    transition: all 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: translateY(-50px) scale(0.9);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1.5rem;
    border-bottom: 1px solid #e0e0e0;
}

.modal-header h3 {
    font-size: 1.3rem;
    font-weight: 600;
    color: #000000;
    transition: color 0.3s ease;
}

.modal-close {
    padding: 0.5rem;
    border: none;
    background-color: transparent;
    cursor: pointer;
    border-radius: 6px;
    transition: all 0.3s ease;
    color: #666666;
}

.modal-close:hover {
    background-color: #f5f5f5;
    color: #000000;
}

.modal-body {
    padding: 1.5rem;
}

.modal-actions {
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
    margin-top: 1.5rem;
}

.cancel-btn,
.save-btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-family: inherit;
    font-size: 0.95rem;
    font-weight: 500;
    cursor: pointer;
    transition: all 0.3s ease;
}

.cancel-btn {
    background-color: #f5f5f5;
    color: #666666;
    border: 1px solid #e0e0e0;
}

.cancel-btn:hover {
    background-color: #e0e0e0;
    color: #333333;
}

.save-btn {
    background-color: #000000;
    color: #ffffff;
}

.save-btn:hover {
    background-color: #333333;
    transform: translateY(-2px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
}

/* Responsive Design */
@media (max-width: 768px) {
    .admin-header {
        padding: 1rem;
    }
    
    .admin-title {
        font-size: 1.2rem;
    }
    
    .sidebar {
        position: fixed;
        left: -250px;
        top: 0;
        height: 100vh;
        z-index: 1500;
        transition: left 0.3s ease;
    }
    
    .sidebar.active {
        left: 0;
    }
    
    .mobile-menu-toggle {
        display: block;
    }
    
    .main-panel {
        padding: 1rem;
        margin-left: 0;
    }
    
    .stats-grid {
        grid-template-columns: 1fr;
    }
    
    .search-filter {
        flex-direction: column;
        align-items: stretch;
    }
    
    .search-input {
        max-width: none;
    }
    
    .data-table-wrapper {
        font-size: 0.8rem;
    }
    
    .data-table th,
    .data-table td {
        padding: 0.5rem;
    }
    
    .modal {
        width: 95%;
        margin: 1rem;
    }
    
    .modal-actions {
        flex-direction: column;
    }
    
    .cancel-btn,
    .save-btn {
        width: 100%;
    }
}

/* Category Badges */
.category-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.25rem;
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 500;
    text-transform: capitalize;
}

.category-timetable {
    background-color: #e3f2fd;
    color: #1565c0;
}

.category-fee {
    background-color: #e8f5e8;
    color: #2e7d32;
}

.category-faculty {
    background-color: #fff3e0;
    color: #ef6c00;
}

.category-notice {
    background-color: #fce4ec;
    color: #c2185b;
}

body.dark-mode .category-timetable {
    background-color: rgba(33, 150, 243, 0.15);
    color: #64b5f6;
    border: 1px solid rgba(33, 150, 243, 0.3);
}

body.dark-mode .category-fee {
    background-color: rgba(76, 175, 80, 0.15);
    color: #81c784;
    border: 1px solid rgba(76, 175, 80, 0.3);
}

body.dark-mode .category-faculty {
    background-color: rgba(255, 152, 0, 0.15);
    color: #ffb74d;
    border: 1px solid rgba(255, 152, 0, 0.3);
}

body.dark-mode .category-notice {
    background-color: rgba(233, 30, 99, 0.15);
    color: #f48fb1;
    border: 1px solid rgba(233, 30, 99, 0.3);
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    right: 20px;
    background-color: #ffffff;
    border: 1px solid #e0e0e0;
    border-radius: 8px;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
    z-index: 3000;
    transform: translateX(400px);
    opacity: 0;
    transition: all 0.3s ease;
    min-width: 300px;
    max-width: 400px;
}

.notification.show {
    transform: translateX(0);
    opacity: 1;
}

.notification-content {
    display: flex;
    align-items: center;
    justify-content: space-between;
    padding: 1rem;
    gap: 1rem;
}

.notification-close {
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    color: #666666;
    padding: 0;
    width: 20px;
    height: 20px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 50%;
    transition: all 0.2s ease;
}

.notification-close:hover {
    background-color: #f5f5f5;
    color: #000000;
}

.notification-success {
    border-left: 4px solid #4caf50;
}

.notification-error {
    border-left: 4px solid #f44336;
}

.notification-info {
    border-left: 4px solid #2196f3;
}

body.dark-mode .notification {
    background-color: #1c1c1c;
    border: 1px solid #404040;
    color: #e0e0e0;
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.6);
}

body.dark-mode .notification-close {
    color: #b0b0b0;
}

body.dark-mode .notification-close:hover {
    background-color: #2a2a2a;
    color: #ffffff;
}

body.dark-mode .notification-success {
    border-left: 4px solid #4caf50;
    background-color: rgba(76, 175, 80, 0.05);
}

body.dark-mode .notification-error {
    border-left: 4px solid #f44336;
    background-color: rgba(244, 67, 54, 0.05);
}

body.dark-mode .notification-info {
    border-left: 4px solid #2196f3;
    background-color: rgba(33, 150, 243, 0.05);
}

@media (max-width: 480px) {
    .header-right {
        gap: 0.5rem;
    }

    .logout-btn span {
        display: none;
    }

    .section-header h2 {
        font-size: 1.5rem;
    }

    .form-container,
    .table-container,
    .settings-container {
        padding: 1rem;
    }

    .notification {
        right: 10px;
        left: 10px;
        min-width: auto;
        max-width: none;
        transform: translateY(-100px);
    }

    .notification.show {
        transform: translateY(0);
    }
}

/* Password Change Styles */
.password-change-section {
    border-top: 1px solid #e0e0e0;
    padding-top: 24px;
    margin-top: 24px;
}

.setting-btn {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 10px 16px;
    background-color: #000000;
    color: #ffffff;
    border: 1px solid #000000;
    border-radius: 6px;
    cursor: pointer;
    font-size: 14px;
    font-weight: 500;
    transition: all 0.2s ease;
}

.setting-btn:hover {
    background-color: #333333;
    border-color: #333333;
    transform: translateY(-1px);
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
}

.setting-btn svg {
    flex-shrink: 0;
}

/* Success Message Styles */
.success-message {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 12px 16px;
    background-color: #f0f9ff;
    border: 1px solid #22c55e;
    border-radius: 6px;
    color: #16a34a;
    font-size: 14px;
    margin-bottom: 16px;
}

.success-message svg {
    flex-shrink: 0;
    color: #22c55e;
}

/* Dark mode styles for password change */
body.dark-mode .password-change-section {
    border-top-color: #404040;
}

body.dark-mode .setting-btn {
    background-color: #ffffff;
    color: #000000;
    border-color: #ffffff;
}

body.dark-mode .setting-btn:hover {
    background-color: #e0e0e0;
    border-color: #e0e0e0;
}

body.dark-mode .success-message {
    background-color: #0f2419;
    border-color: #22c55e;
    color: #4ade80;
}
